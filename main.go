package main

import (
	"time"

	"victoriaMetricsCollector/global"
	"victoriaMetricsCollector/shrcb/prometheus"
	"victoriaMetricsCollector/shrcb/uas2g"
)

func init() {
	// 初始化全局配置
	global.Setting.Init()
}

func main() {

	Prome()
	for {
		time.Sleep(time.Hour * 24)
	}
}

func Uas2g() {
	port := global.Setting.StaticConfig.UAS2G.Port
	uas2g.NewUSA2G(port)
}

func Prome() {
	// 从全局配置中获取Prometheus HTTP服务端口
	gprometheusHttpPort := global.Setting.StaticConfig.Prometheus.Port
	// 从全局配置中获取抓取间隔
	scrapeInterval := global.Setting.StaticConfig.Prometheus.ScrapeInterval
	// 从全局配置中获取缓存数据量
	scrapeBufferSize := global.Setting.StaticConfig.Prometheus.ScrapeBufferSize

	// 启动Prometheus动态指标收集器
	prometheus.StartCollector(gprometheusHttpPort, scrapeInterval, scrapeBufferSize)
}
